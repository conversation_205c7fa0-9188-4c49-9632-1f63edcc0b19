# yaml-language-server: $schema=https://app.stainless.com/config-internal.schema.json

organization:
  name: opencode
  docs: "https://opencode.ai/docs"
  contact: "<EMAIL>"

targets:
  typescript:
    package_name: "@opencode-ai/sdk"
    production_repo: "sst/opencode-sdk-js"
    publish:
      npm: true
  go:
    package_name: opencode
    production_repo: sst/opencode-sdk-go
  python:
    project_name: opencode-ai
    package_name: opencode_ai
    production_repo: sst/opencode-sdk-python
    publish:
      pypi: true

environments:
  production: http://localhost:54321

streaming:
  on_event:
    - kind: fallthrough
      handle: yield

resources:
  $shared:
    models:
      unknownError: UnknownError
      providerAuthError: ProviderAuthError
      messageAbortedError: MessageAbortedError

  event:
    methods:
      list:
        endpoint: get /event
        paginated: false
        streaming:
          # This method is always streaming.
          param_discriminator: null

  app:
    models:
      app: App
      logLevel: LogLevel
      provider: Provider
      model: Model
      mode: Mode
    methods:
      get: get /app
      init: post /app/init
      log: post /log
      modes: get /mode
      providers: get /config/providers

  find:
    models:
      match: Match
      symbol: Symbol
    methods:
      text: get /find
      files: get /find/file
      symbols: get /find/symbol

  file:
    models:
      file: File
    methods:
      read: get /file
      status: get /file/status

  config:
    models:
      config: Config
      keybindsConfig: KeybindsConfig
      mcpLocalConfig: McpLocalConfig
      mcpRemoteConfig: McpRemoteConfig
      modeConfig: ModeConfig
    methods:
      get: get /config

  session:
    models:
      session: Session
      message: Message
      part: Part
      textPart: TextPart
      textPartInput: TextPartInput
      filePart: FilePart
      filePartInput: FilePartInput
      filePartSourceText: FilePartSourceText
      filePartSource: FilePartSource
      fileSource: FileSource
      symbolSource: SymbolSource
      toolPart: ToolPart
      stepStartPart: StepStartPart
      stepFinishPart: StepFinishPart
      snapshotPart: SnapshotPart
      assistantMessage: AssistantMessage
      userMessage: UserMessage
      toolStatePending: ToolStatePending
      toolStateRunning: ToolStateRunning
      toolStateCompleted: ToolStateCompleted
      toolStateError: ToolStateError

    methods:
      list: get /session
      create: post /session
      delete: delete /session/{id}
      init: post /session/{id}/init
      abort: post /session/{id}/abort
      share: post /session/{id}/share
      unshare: delete /session/{id}/share
      summarize: post /session/{id}/summarize
      messages: get /session/{id}/message
      chat: post /session/{id}/message
      revert: post /session/{id}/revert
      unrevert: post /session/{id}/unrevert

  tui:
    methods:
      appendPrompt: post /tui/append-prompt
      openHelp: post /tui/open-help

settings:
  disable_mock_tests: true
  license: MIT

security:
  - {}

readme:
  example_requests:
    default:
      type: request
      endpoint: get /session
      params: {}
    headline:
      type: request
      endpoint: get /session
      params: {}
    streaming:
      type: request
      endpoint: get /event
      params: {}
