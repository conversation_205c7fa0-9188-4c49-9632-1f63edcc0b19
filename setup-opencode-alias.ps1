# OpenCode Global Alias Setup Script
# This creates a global 'opencode' command that works from anywhere

Write-Host "Setting up OpenCode global alias..." -ForegroundColor Green

# Define the OpenCode command
$opencodeCommand = @"
function opencode {
    `$originalPath = Get-Location
    try {
        Set-Location "C:\Users\<USER>\Downloads\opencode-dev"
        `$env:PATH += ";C:\Users\<USER>\.bun\bin;C:\Program Files\Go\bin"
        & bun run packages/opencode/src/index.ts `$args
    }
    finally {
        Set-Location `$originalPath
    }
}
"@

# Get PowerShell profile path
$profilePath = $PROFILE.CurrentUserAllHosts

# Create profile directory if it doesn't exist
$profileDir = Split-Path $profilePath -Parent
if (!(Test-Path $profileDir)) {
    New-Item -ItemType Directory -Path $profileDir -Force | Out-Null
}

# Check if alias already exists
if (Test-Path $profilePath) {
    $profileContent = Get-Content $profilePath -Raw
    if ($profileContent -like "*function opencode*") {
        Write-Host "OpenCode alias already exists in profile. Updating..." -ForegroundColor Yellow
        # Remove existing function and add new one
        $profileContent = $profileContent -replace "function opencode \{[^}]*\}", ""
        $profileContent = $profileContent.Trim()
        if ($profileContent) {
            $profileContent += "`n`n"
        }
        $profileContent += $opencodeCommand
        Set-Content -Path $profilePath -Value $profileContent
    } else {
        Write-Host "Adding OpenCode alias to existing profile..." -ForegroundColor Yellow
        Add-Content -Path $profilePath -Value "`n`n# OpenCode alias`n$opencodeCommand"
    }
} else {
    Write-Host "Creating new PowerShell profile with OpenCode alias..." -ForegroundColor Yellow
    Set-Content -Path $profilePath -Value "# PowerShell Profile`n`n# OpenCode alias`n$opencodeCommand"
}

Write-Host "`nSetup complete! " -ForegroundColor Green -NoNewline
Write-Host "Please restart PowerShell or run: " -ForegroundColor White -NoNewline
Write-Host ". `$PROFILE" -ForegroundColor Cyan

Write-Host "`nAfter restart, you can use 'opencode' from anywhere!" -ForegroundColor Green
Write-Host "Example: " -ForegroundColor White -NoNewline
Write-Host "opencode --help" -ForegroundColor Cyan
