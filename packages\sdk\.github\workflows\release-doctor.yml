name: Release Doctor
on:
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  release_doctor:
    name: release doctor
    runs-on: ubuntu-latest
    if: github.repository == 'sst/opencode-sdk-js' && (github.event_name == 'push' || github.event_name == 'workflow_dispatch' || startsWith(github.head_ref, 'release-please') || github.head_ref == 'next')

    steps:
      - uses: actions/checkout@v4

      - name: Check release environment
        run: |
          bash ./bin/check-release-environment
        env:
          NPM_TOKEN: ${{ secrets.OPENCODE_NPM_TOKEN || secrets.NPM_TOKEN }}
