name: CI
on:
  push:
    branches-ignore:
      - 'generated'
      - 'codegen/**'
      - 'integrated/**'
      - 'stl-preview-head/**'
      - 'stl-preview-base/**'
  pull_request:
    branches-ignore:
      - 'stl-preview-head/**'
      - 'stl-preview-base/**'

jobs:
  lint:
    timeout-minutes: 10
    name: lint
    runs-on: ${{ github.repository == 'stainless-sdks/opencode-typescript' && 'depot-ubuntu-24.04' || 'ubuntu-latest' }}
    if: github.event_name == 'push' || github.event.pull_request.head.repo.fork
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Bootstrap
        run: ./scripts/bootstrap

      - name: Check types
        run: ./scripts/lint

  build:
    timeout-minutes: 5
    name: build
    runs-on: ${{ github.repository == 'stainless-sdks/opencode-typescript' && 'depot-ubuntu-24.04' || 'ubuntu-latest' }}
    if: github.event_name == 'push' || github.event.pull_request.head.repo.fork
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Bootstrap
        run: ./scripts/bootstrap

      - name: Check build
        run: ./scripts/build

      - name: Get GitHub OIDC Token
        if: github.repository == 'stainless-sdks/opencode-typescript'
        id: github-oidc
        uses: actions/github-script@v6
        with:
          script: core.setOutput('github_token', await core.getIDToken());

      - name: Upload tarball
        if: github.repository == 'stainless-sdks/opencode-typescript'
        env:
          URL: https://pkg.stainless.com/s
          AUTH: ${{ steps.github-oidc.outputs.github_token }}
          SHA: ${{ github.sha }}
        run: ./scripts/utils/upload-artifact.sh
  test:
    timeout-minutes: 10
    name: test
    runs-on: ${{ github.repository == 'stainless-sdks/opencode-typescript' && 'depot-ubuntu-24.04' || 'ubuntu-latest' }}
    if: github.event_name == 'push' || github.event.pull_request.head.repo.fork
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Bootstrap
        run: ./scripts/bootstrap

      - name: Run tests
        run: ./scripts/test
