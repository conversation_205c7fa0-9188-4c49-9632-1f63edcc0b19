name: publish-github-action

on:
  workflow_dispatch:
  push:
    tags:
      - "github-v*.*.*"
      - "!github-v1"

concurrency: ${{ github.workflow }}-${{ github.ref }}

permissions:
  contents: write

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - run: git fetch --force --tags

      - name: Publish
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "opencode"
          ./script/publish
        working-directory: ./github
