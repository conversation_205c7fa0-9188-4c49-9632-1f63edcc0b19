:root {
  --sl-color-bg-surface: var(--sl-color-bg-nav);
  --sl-color-divider: var(--sl-color-gray-5);
}

.expressive-code .frame {
  box-shadow: none;
}

@media (prefers-color-scheme: dark) {
  .shiki,
  .shiki span {
    color: var(--shiki-dark) !important;
    background-color: var(--shiki-dark-bg) !important;
    /* Optional, if you also want font styles */
    font-style: var(--shiki-dark-font-style) !important;
    font-weight: var(--shiki-dark-font-weight) !important;
    text-decoration: var(--shiki-dark-text-decoration) !important;
  }
}
