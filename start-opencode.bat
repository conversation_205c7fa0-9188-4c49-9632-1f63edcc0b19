@echo off
title OpenCode Development Environment
color 0A

echo.
echo ========================================
echo   OpenCode Development Environment
echo ========================================
echo.
echo Choose an option:
echo [1] Start Development Server
echo [2] Install OpenCode CLI Globally
echo [3] Show Usage Instructions
echo [4] Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto :dev_server
if "%choice%"=="2" goto :install_global
if "%choice%"=="3" goto :show_instructions
if "%choice%"=="4" goto :exit
echo Invalid choice. Please try again.
pause
goto :start

:dev_server
echo.
echo ========================================
echo   Starting Development Server
echo ========================================
echo.

REM Set up environment
cd /d "C:\Users\<USER>\Downloads\opencode-dev"
set PATH=%PATH%;C:\Users\<USER>\.bun\bin;C:\Program Files\Go\bin

REM Check if tools are available
echo [1/4] Checking Bun...
bun --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Bun not found! Please ensure Bun is installed.
    pause
    exit /b 1
)
echo ✓ Bun is available

echo [2/4] Checking Go...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go not found! Please ensure Go is installed.
    pause
    exit /b 1
)
echo ✓ Go is available

echo [3/4] Checking dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    bun install
)
echo ✓ Dependencies ready

echo [4/4] Starting OpenCode development server...
echo.
echo ----------------------------------------
echo  Development server starting...
echo  Press Ctrl+C to stop
echo ----------------------------------------
echo.

bun run dev
goto :end

:install_global
echo.
echo ========================================
echo   Installing OpenCode CLI Globally
echo ========================================
echo.
echo Note: The npm package has Windows compatibility issues.
echo Here are alternative installation methods:
echo.
echo Method 1: Build from source (Recommended for Windows)
echo   1. Use the development server (Option 1)
echo   2. Create an alias: doskey opencode=cd /d "C:\Users\<USER>\Downloads\opencode-dev" ^&^& bun run packages/opencode/src/index.ts $*
echo.
echo Method 2: Manual binary installation
echo   1. Download the latest release from: https://github.com/sst/opencode/releases
echo   2. Extract to a folder in your PATH
echo.
echo Method 3: WSL (Windows Subsystem for Linux)
echo   1. Install WSL: wsl --install
echo   2. In WSL: curl -fsSL https://opencode.ai/install ^| bash
echo.
echo Press any key to return to main menu...
pause >nul
goto :start

:show_instructions
echo.
echo ========================================
echo   OpenCode Usage Instructions
echo ========================================
echo.
echo DEVELOPMENT MODE:
echo   - Run this script and choose option 1
echo   - Or manually: bun run dev
echo   - Or directly: bun run packages/opencode/src/index.ts
echo.
echo TESTING COMMANDS:
echo   - Run tests: bun test
echo   - Type check: bun run typecheck
echo   - Build TUI: cd packages/tui ^&^& go build ./cmd/opencode
echo.
echo PROJECT STRUCTURE:
echo   - packages/opencode: Main CLI application
echo   - packages/tui: Terminal UI (Go)
echo   - packages/sdk: SDK for integrations
echo   - packages/web: Documentation website
echo.
echo GLOBAL CLI (when available):
echo   - Initialize project: opencode
echo   - Run command: /init
echo.
echo Press any key to return to main menu...
pause >nul
goto :start

:exit
echo.
echo Goodbye!
goto :end

:start
cls
goto :main

:main
@echo off
title OpenCode Development Environment
color 0A

echo.
echo ========================================
echo   OpenCode Development Environment
echo ========================================
echo.
echo Choose an option:
echo [1] Start Development Server
echo [2] Install OpenCode CLI Globally
echo [3] Show Usage Instructions
echo [4] Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto :dev_server
if "%choice%"=="2" goto :install_global
if "%choice%"=="3" goto :show_instructions
if "%choice%"=="4" goto :exit
echo Invalid choice. Please try again.
pause
goto :start

:end
