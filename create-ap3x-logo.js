#!/usr/bin/env node

// Custom AP3X logo creator with AP3 in white and X in red
import { render } from 'oh-my-logo';
import chalk from 'chalk';

async function createAP3XLogo() {
  try {
    console.log('\n🎨 Creating AP3X Logo...\n');
    
    // Create AP3 part in white/light colors
    const ap3Logo = await render('AP3', {
      palette: ['#ffffff', '#f0f0f0', '#e0e0e0'], // White gradient
      direction: 'horizontal',
      font: 'Standard'
    });
    
    // Create X part in red
    const xLogo = await render('X', {
      palette: ['#ff0000', '#ff3333', '#ff6666'], // Red gradient
      direction: 'horizontal', 
      font: 'Standard'
    });
    
    // Split into lines for combining
    const ap3Lines = ap3Logo.split('\n');
    const xLines = xLogo.split('\n');
    
    // Combine AP3 and X horizontally
    const maxLines = Math.max(ap3Lines.length, xLines.length);
    const combinedLines = [];
    
    for (let i = 0; i < maxLines; i++) {
      const ap3Line = ap3Lines[i] || '';
      const xLine = xLines[i] || '';
      combinedLines.push(ap3Line + xLine);
    }
    
    console.log(combinedLines.join('\n'));
    console.log('\n✨ AP3X Logo Complete!\n');
    
    // Also show some variations
    console.log('🔥 Filled version:');
    
  } catch (error) {
    console.error('Error creating logo:', error.message);
    
    // Fallback: Simple colored text
    console.log('\n📝 Fallback AP3X Logo:');
    console.log(chalk.white.bold('AP3') + chalk.red.bold('X'));
    console.log('\n');
  }
}

// Alternative simpler approach using chalk
function createSimpleAP3XLogo() {
  console.log('\n🎯 Simple AP3X Logo:\n');
  
  const ap3 = chalk.white.bold(`
     _    ____  _____
    / \\  |  _ \\|___ /
   / _ \\ | |_) | |_ \\
  / ___ \\|  __/ ___) |
 /_/   \\_\\_|   |____/`);

  const x = chalk.red.bold(`
 __  __
 \\ \\/ /
  \\  /
  /  \\
 /_/\\_\\`);

  // Combine them side by side
  const ap3Lines = ap3.split('\n');
  const xLines = x.split('\n');
  const maxLines = Math.max(ap3Lines.length, xLines.length);
  
  for (let i = 0; i < maxLines; i++) {
    const ap3Line = ap3Lines[i] || '';
    const xLine = xLines[i] || '';
    console.log(ap3Line + '  ' + xLine);
  }
  
  console.log('\n✨ Simple AP3X Logo Complete!\n');
}

// Run both versions
createAP3XLogo().then(() => {
  createSimpleAP3XLogo();
}).catch(() => {
  createSimpleAP3XLogo();
});
