#!/usr/bin/env node

import chalk from 'chalk';

console.log('\n🎨 AP3X Logo Collection\n');

// Version 1: Large ASCII Art Style
console.log('🔥 Version 1: Large ASCII Art');
console.log(chalk.white.bold(`
     _    ____  _____`) + chalk.red.bold(`  __  __
    / \\  |  _ \\|___ /`) + chalk.red.bold(`  \\ \\/ /
   / _ \\ | |_) | |_ \\`) + chalk.red.bold(`   \\  /
  / ___ \\|  __/ ___) |`) + chalk.red.bold(`   /  \\
 /_/   \\_\\_|   |____/`) + chalk.red.bold(`  /_/\\_\\`));

console.log('\n' + '='.repeat(50) + '\n');

// Version 2: Block Style
console.log('🎯 Version 2: Block Style');
console.log(chalk.white.bold(`
 ██████╗ ██████╗ ██████╗`) + chalk.red.bold(` ██╗  ██╗
██╔══██╗██╔══██╗╚════██╗`) + chalk.red.bold(`╚██╗██╔╝
███████║██████╔╝ █████╔╝`) + chalk.red.bold(` ╚███╔╝ 
██╔══██║██╔═══╝  ╚═══██╗`) + chalk.red.bold(` ██╔██╗ 
██║  ██║██║     ██████╔╝`) + chalk.red.bold(`██╔╝ ██╗
╚═╝  ╚═╝╚═╝     ╚═════╝ `) + chalk.red.bold(`╚═╝  ╚═╝`));

console.log('\n' + '='.repeat(50) + '\n');

// Version 3: Compact Style
console.log('⚡ Version 3: Compact Style');
console.log(chalk.white.bold('AP3') + chalk.red.bold('X') + ' - ' + 
           chalk.gray('Simple and clean'));

console.log('\n' + '='.repeat(50) + '\n');

// Version 4: Stylized
console.log('✨ Version 4: Stylized');
console.log(chalk.white.bold(`
 ▄▀█ █▀█ ▀█`) + chalk.red.bold(` ▀▄▀
 █▀█ █▀▀ █▄`) + chalk.red.bold(` █▀█`));

console.log('\n' + '='.repeat(50) + '\n');

// Version 5: Banner Style
console.log('🚀 Version 5: Banner Style');
console.log(chalk.bgWhite.black.bold(' AP3 ') + chalk.bgRed.white.bold(' X '));

console.log('\n' + '='.repeat(50) + '\n');

// Version 6: Gradient Effect Simulation
console.log('🌈 Version 6: Gradient Effect');
const ap3Gradient = chalk.hex('#ffffff').bold('A') + 
                   chalk.hex('#f0f0f0').bold('P') + 
                   chalk.hex('#e0e0e0').bold('3');
const xGradient = chalk.hex('#ff0000').bold('X');

console.log(`
 ${ap3Gradient}${xGradient} - Gradient simulation
 ${chalk.white.bold('AP3')}${chalk.red.bold('X')} - High contrast version`);

console.log('\n✨ Choose your favorite AP3X logo! ✨\n');

// Bonus: Show usage examples
console.log('💡 Usage Examples:');
console.log('• README headers');
console.log('• Terminal startup banners'); 
console.log('• CLI application branding');
console.log('• Project documentation');
console.log('');
