#!/usr/bin/env bash

errors=()

if [ -z "${NPM_TOKEN}" ]; then
  errors+=("The NPM_TOKEN secret has not been set. Please set it in either this repository's secrets or your organization secrets")
fi

lenErrors=${#errors[@]}

if [[ lenErrors -gt 0 ]]; then
  echo -e "Found the following errors in the release environment:\n"

  for error in "${errors[@]}"; do
    echo -e "- $error\n"
  done

  exit 1
fi

echo "The environment is ready to push releases!"

