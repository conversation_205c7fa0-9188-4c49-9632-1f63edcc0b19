---
title: Modes
description: Different modes for different use cases.
---

Modes in opencode allow you to customize the behavior, tools, and prompts for different use cases.

It comes with two built-in modes: **build** and **plan**. You can customize
these or configure your own through the opencode config.

:::tip
Use the plan mode to analyze code and review suggestions without making any code
changes.
:::

You can switch between modes during a session or configure them in your config file.

---

## Built-in

opencode comes with two built-in modes.

---

### Build

Build is the **default** mode with all tools enabled. This is the standard mode for development work where you need full access to file operations and system commands.

---

### Plan

A restricted mode designed for planning and analysis. In plan mode, the following tools are disabled by default:

- `write` - Cannot create new files
- `edit` - Cannot modify existing files
- `patch` - Cannot apply patches
- `bash` - Cannot execute shell commands

This mode is useful when you want the AI to analyze code, suggest changes, or create plans without making any actual modifications to your codebase.

---

## Switching

You can switch between modes during a session using the _Tab_ key. Or your configured `switch_mode` keybind.

---

## Configure

You can customize the built-in modes or create your own in the opencode [config](/docs/config).

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "mode": {
    "build": {
      "model": "anthropic/claude-sonnet-4-20250514",
      "prompt": "{file:./prompts/build.txt}",
      "tools": {
        "write": true,
        "edit": true,
        "bash": true
      }
    },
    "plan": {
      "model": "anthropic/claude-haiku-4-20250514",
      "tools": {
        "write": false,
        "edit": false,
        "bash": false
      }
    }
  }
}
```

Let's look at these options in detail.

---

### Model

Use the `model` config to override the default model for this mode. Useful for using different models optimized for different tasks. For example, a faster model for planning, a more capable model for implementation.

```json title="opencode.json"
{
  "mode": {
    "plan": {
      "model": "anthropic/claude-haiku-4-20250514"
    }
  }
}
```

---

### Temperature

Control the randomness and creativity of the AI's responses with the `temperature` config. Lower values make responses more focused and deterministic, while higher values increase creativity and variability.

```json title="opencode.json"
{
  "mode": {
    "plan": {
      "temperature": 0.1
    },
    "creative": {
      "temperature": 0.8
    }
  }
}
```

Temperature values typically range from 0.0 to 1.0:

- **0.0-0.2**: Very focused and deterministic responses, ideal for code analysis and planning
- **0.3-0.5**: Balanced responses with some creativity, good for general development tasks
- **0.6-1.0**: More creative and varied responses, useful for brainstorming and exploration

```json title="opencode.json"
{
  "mode": {
    "analyze": {
      "temperature": 0.1,
      "prompt": "{file:./prompts/analysis.txt}"
    },
    "build": {
      "temperature": 0.3
    },
    "brainstorm": {
      "temperature": 0.7,
      "prompt": "{file:./prompts/creative.txt}"
    }
  }
}
```

If no temperature is specified, opencode uses model-specific defaults (typically 0 for most models, 0.55 for Qwen models).

---

### Prompt

Specify a custom system prompt file for this mode with the `prompt` config. The prompt file should contain instructions specific to the mode's purpose.

```json title="opencode.json"
{
  "mode": {
    "review": {
      "prompt": "{file:./prompts/code-review.txt}"
    }
  }
}
```

This path is relative to where the config file is located. So this works for
both the global opencode config and the project specific config.

---

### Tools

Control which tools are available in this mode with the `tools` config. You can enable or disable specific tools by setting them to `true` or `false`.

```json
{
  "mode": {
    "readonly": {
      "tools": {
        "write": false,
        "edit": false,
        "bash": false,
        "read": true,
        "grep": true,
        "glob": true
      }
    }
  }
}
```

If no tools are specified, all tools are enabled by default.

---

#### Available tools

Here are all the tools can be controlled through the mode config.

| Tool        | Description             |
| ----------- | ----------------------- |
| `bash`      | Execute shell commands  |
| `edit`      | Modify existing files   |
| `write`     | Create new files        |
| `read`      | Read file contents      |
| `grep`      | Search file contents    |
| `glob`      | Find files by pattern   |
| `list`      | List directory contents |
| `patch`     | Apply patches to files  |
| `todowrite` | Manage todo lists       |
| `todoread`  | Read todo lists         |
| `webfetch`  | Fetch web content       |

---

## Custom modes

You can create your own custom modes by adding them to the `mode` configuration. For example, a documentation mode that focuses on reading and analysis.

```json title="opencode.json" {4-14}
{
  "$schema": "https://opencode.ai/config.json",
  "mode": {
    "docs": {
      "prompt": "{file:./prompts/documentation.txt}",
      "tools": {
        "write": true,
        "edit": true,
        "bash": false,
        "read": true,
        "grep": true,
        "glob": true
      }
    }
  }
}
```

---

### Use cases

Here are some common use cases for different modes.

- **Build mode**: Full development work with all tools enabled
- **Plan mode**: Analysis and planning without making changes
- **Review mode**: Code review with read-only access plus documentation tools
- **Debug mode**: Focused on investigation with bash and read tools enabled
- **Docs mode**: Documentation writing with file operations but no system commands

You might also find different models are good for different use cases.
