#!/bin/bash

set -e

# Parse command line arguments
DEV_MODE=false
for arg in "$@"; do
    if [ "$arg" = "--dev" ]; then
        DEV_MODE=true
    fi
done

bun run ./packages/opencode/src/index.ts generate > openapi.json

echo "Running stl builds create..."
stl builds create --branch dev --pull --allow-empty --+target go --+target typescript

echo "Cleaning up..."
rm -rf packages/tui/sdk
mv opencode-go/ packages/tui/sdk/
rm -rf packages/tui/sdk/.git
rm -rf packages/sdk
mv opencode-typescript/ packages/sdk/
rm -rf packages/sdk/.git

# Only run production build if not in dev mode
if [ "$DEV_MODE" = false ]; then
    echo "Kicking off production build..."
    stl builds create --branch main --wait=false
else
    echo "Skipping production build (--dev flag detected)"
fi

echo "Done!"

