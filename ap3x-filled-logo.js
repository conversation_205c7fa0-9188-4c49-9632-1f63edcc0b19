#!/usr/bin/env node

import chalk from 'chalk';

console.log('\n🎨 AP3X Filled Logo\n');

// Filled block version of the first logo style
console.log('🔥 AP3X - Filled Block Style');
console.log(chalk.white.bold(`
 █████╗ ██████╗ ██████╗`) + chalk.red.bold(` ██╗  ██╗
██╔══██╗██╔══██╗╚════██╗`) + chalk.red.bold(`╚██╗██╔╝
███████║██████╔╝ █████╔╝`) + chalk.red.bold(` ╚███╔╝ 
██╔══██║██╔═══╝  ╚═══██╗`) + chalk.red.bold(` ██╔██╗ 
██║  ██║██║     ██████╔╝`) + chalk.red.bold(`██╔╝ ██╗
╚═╝  ╚═╝╚═╝     ╚═════╝ `) + chalk.red.bold(`╚═╝  ╚═╝`));

console.log('\n' + '='.repeat(50) + '\n');

// Alternative filled style - more compact
console.log('⚡ AP3X - Compact Filled');
console.log(chalk.white.bold(`
 ▄▀█ █▀█ ▀▀▀█`) + chalk.red.bold(` ▀▄▀
 █▀█ █▀▀ ▄▄▄█`) + chalk.red.bold(` █▀█`));

console.log('\n' + '='.repeat(50) + '\n');

// Heavy block style
console.log('💪 AP3X - Heavy Block Style');
console.log(chalk.white.bold(`
 ██████  ██████  ██████`) + chalk.red.bold(`  ██  ██
 ██  ██  ██  ██      ██`) + chalk.red.bold(`   ████ 
 ██████  ██████   █████`) + chalk.red.bold(`    ██  
 ██  ██  ██      ██  ██`) + chalk.red.bold(`   ████ 
 ██  ██  ██      ██████`) + chalk.red.bold(`  ██  ██`));

console.log('\n' + '='.repeat(50) + '\n');

// Super filled version
console.log('🚀 AP3X - Super Filled');
console.log(chalk.white.bold(`
 ████████ ████████ ████████`) + chalk.red.bold(` ████████
 ██    ██ ██    ██       ██`) + chalk.red.bold(` ██    ██
 ████████ ████████  ███████`) + chalk.red.bold(`  ██████ 
 ██    ██ ██       ██    ██`) + chalk.red.bold(`  ██████ 
 ██    ██ ██       ████████`) + chalk.red.bold(` ██    ██`));

console.log('\n✨ Your filled AP3X logos are ready! ✨\n');

// Show the oh-my-logo filled command
console.log('💡 You can also generate filled versions with:');
console.log(chalk.cyan('npx oh-my-logo "AP3X" fire --filled'));
console.log(chalk.cyan('npx oh-my-logo "AP3X" mono --filled'));
console.log('');
