#!/usr/bin/env node

import { render } from 'oh-my-logo';
import chalk from 'chalk';

async function createSplitColorLogo() {
  try {
    console.log('\n🎨 Creating AP3X with AP3 in White and X in Red...\n');
    
    // Generate AP3 in white
    const ap3Logo = await render('AP3', {
      palette: ['#ffffff', '#ffffff', '#ffffff'], // Pure white
      direction: 'horizontal',
      font: 'Standard'
    });
    
    // Generate X in red  
    const xLogo = await render('X', {
      palette: ['#ff0000', '#ff0000', '#ff0000'], // Pure red
      direction: 'horizontal',
      font: 'Standard'
    });
    
    // Split into lines
    const ap3Lines = ap3Logo.split('\n');
    const xLines = xLogo.split('\n');
    
    // Find the maximum number of lines
    const maxLines = Math.max(ap3Lines.length, xLines.length);
    
    console.log('🔥 AP3X Logo - Split Colors (Outlined):');
    console.log('');
    
    // Combine line by line
    for (let i = 0; i < maxLines; i++) {
      const ap3Line = ap3Lines[i] || '';
      const xLine = xLines[i] || '';
      console.log(ap3Line + xLine);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Now create filled version manually
    console.log('🚀 AP3X Logo - Split Colors (Filled):');
    console.log('');
    
    // Manually create filled version with proper spacing
    const filledAP3 = chalk.white.bold(`
 █████╗ ██████╗ ██████╗`);
    
    const filledX = chalk.red.bold(` ██╗  ██╗
██╔══██╗██╔══██╗╚════██╗`) + chalk.red.bold(`╚██╗██╔╝
███████║██████╔╝ █████╔╝`) + chalk.red.bold(` ╚███╔╝ 
██╔══██║██╔═══╝  ╚═══██╗`) + chalk.red.bold(` ██╔██╗ 
██║  ██║██║     ██████╔╝`) + chalk.red.bold(`██╔╝ ██╗
╚═╝  ╚═╝╚═╝     ╚═════╝ `) + chalk.red.bold(`╚═╝  ╚═╝`);
    
    // Split and combine properly
    const ap3FilledLines = filledAP3.split('\n');
    const xFilledLines = filledX.split('\n');
    const maxFilledLines = Math.max(ap3FilledLines.length, xFilledLines.length);
    
    for (let i = 0; i < maxFilledLines; i++) {
      const ap3Line = ap3FilledLines[i] || '';
      const xLine = xFilledLines[i] || '';
      console.log(ap3Line + xLine);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Create a cleaner version
    console.log('✨ AP3X Logo - Clean Split Colors (Filled):');
    console.log('');
    
    console.log(chalk.white.bold(' █████╗ ██████╗ ██████╗') + chalk.red.bold(' ██╗  ██╗'));
    console.log(chalk.white.bold('██╔══██╗██╔══██╗╚════██╗') + chalk.red.bold('╚██╗██╔╝'));
    console.log(chalk.white.bold('███████║██████╔╝ █████╔╝') + chalk.red.bold(' ╚███╔╝ '));
    console.log(chalk.white.bold('██╔══██║██╔═══╝  ╚═══██╗') + chalk.red.bold(' ██╔██╗ '));
    console.log(chalk.white.bold('██║  ██║██║     ██████╔╝') + chalk.red.bold('██╔╝ ██╗'));
    console.log(chalk.white.bold('╚═╝  ╚═╝╚═╝     ╚═════╝ ') + chalk.red.bold('╚═╝  ╚═╝'));
    
    console.log('\n✅ Perfect! AP3 in white, X in red! ✅\n');
    
  } catch (error) {
    console.error('Error:', error.message);
    
    // Fallback manual version
    console.log('\n🎯 Fallback AP3X Logo - Manual Split Colors:\n');
    
    console.log(chalk.white.bold(' █████╗ ██████╗ ██████╗') + chalk.red.bold(' ██╗  ██╗'));
    console.log(chalk.white.bold('██╔══██╗██╔══██╗╚════██╗') + chalk.red.bold('╚██╗██╔╝'));
    console.log(chalk.white.bold('███████║██████╔╝ █████╔╝') + chalk.red.bold(' ╚███╔╝ '));
    console.log(chalk.white.bold('██╔══██║██╔═══╝  ╚═══██╗') + chalk.red.bold(' ██╔██╗ '));
    console.log(chalk.white.bold('██║  ██║██║     ██████╔╝') + chalk.red.bold('██╔╝ ██╗'));
    console.log(chalk.white.bold('╚═╝  ╚═╝╚═╝     ╚═════╝ ') + chalk.red.bold('╚═╝  ╚═╝'));
    
    console.log('\n✅ AP3 in white, X in red! ✅\n');
  }
}

createSplitColorLogo();
