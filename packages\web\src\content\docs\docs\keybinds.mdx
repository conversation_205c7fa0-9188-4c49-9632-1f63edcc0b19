---
title: Keybinds
description: Customize your keybinds.
---

opencode has a list of keybinds that you can customize through the opencode config.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "keybinds": {
    "leader": "ctrl+x",
    "app_help": "<leader>h",
    "switch_mode": "tab",

    "editor_open": "<leader>e",

    "session_new": "<leader>n",
    "session_list": "<leader>l",
    "session_share": "<leader>s",
    "session_unshare": "<leader>u",
    "session_interrupt": "esc",
    "session_compact": "<leader>c",

    "tool_details": "<leader>d",
    "model_list": "<leader>m",
    "theme_list": "<leader>t",
    "project_init": "<leader>i",

    "input_clear": "ctrl+c",
    "input_paste": "ctrl+v",
    "input_submit": "enter",
    "input_newline": "shift+enter,ctrl+j",

    "messages_page_up": "pgup",
    "messages_page_down": "pgdown",
    "messages_half_page_up": "ctrl+alt+u",
    "messages_half_page_down": "ctrl+alt+d",
    "messages_first": "ctrl+g",
    "messages_last": "ctrl+alt+g",
    "messages_copy": "<leader>y",

    "app_exit": "ctrl+c,<leader>q"
  }
}
```

## Leader key

opencode uses a `leader` key for most keybinds. This avoids conflicts in your terminal.

By default, `ctrl+x` is the leader key and most actions require you to first press the leader key and then the shortcut. For example, to start a new session you first press `ctrl+x` and then press `n`.

You don't need to use a leader key for your keybinds but we recommend doing so.

## Disable a keybind

You can disable a keybind by adding the key to your config with a value of "none".

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "keybinds": {
    "session_compact": "none",
  }
}
```
