#!/usr/bin/env node

import chalk from 'chalk';

console.log('\n🎯 AP3X Logo - Final Version\n');
console.log('AP3 in WHITE, X in RED - Filled Style\n');

// The perfect AP3X logo with split colors
console.log(chalk.white.bold(' █████╗ ██████╗ ██████╗') + chalk.red.bold(' ██╗  ██╗'));
console.log(chalk.white.bold('██╔══██╗██╔══██╗╚════██╗') + chalk.red.bold('╚██╗██╔╝'));
console.log(chalk.white.bold('███████║██████╔╝ █████╔╝') + chalk.red.bold(' ╚███╔╝ '));
console.log(chalk.white.bold('██╔══██║██╔═══╝  ╚═══██╗') + chalk.red.bold(' ██╔██╗ '));
console.log(chalk.white.bold('██║  ██║██║     ██████╔╝') + chalk.red.bold('██╔╝ ██╗'));
console.log(chalk.white.bold('╚═╝  ╚═╝╚═╝     ╚═════╝ ') + chalk.red.bold('╚═╝  ╚═╝'));

console.log('\n✅ Perfect split-color logo! ✅');
console.log('🔸 AP3 = White');
console.log('🔸 X = Red');
console.log('🔸 Filled block style\n');
